@echo off
REM Batch file to launch Allegro and load SKILL program for 3D symbol generation
REM Author: Generated for APL to SKILL integration project
REM Date: %date%

echo Starting Allegro PCB Editor with 3D Symbol Generation...
echo.

REM Set Allegro installation path (modify as needed)
set ALLEGRO_PATH="C:\Cadence\SPB_17.4\tools\bin"
set SKILL_SCRIPT_PATH="%~dp0generate_3d_symbol.il"

REM Check if Allegro exists
if not exist %ALLEGRO_PATH%\allegro.exe (
    echo ERROR: Allegro not found at %ALLEGRO_PATH%
    echo Please modify ALLEGRO_PATH in this batch file
    pause
    exit /b 1
)

REM Check if SKILL script exists
if not exist %SKILL_SCRIPT_PATH% (
    echo ERROR: SKILL script not found at %SKILL_SCRIPT_PATH%
    echo Please ensure generate_3d_symbol.il is in the same directory
    pause
    exit /b 1
)

echo Loading Allegro PCB Editor...
echo SKILL Script: %SKILL_SCRIPT_PATH%
echo.

REM Launch Allegro with the SKILL script
cd /d %ALLEGRO_PATH%
allegro.exe -skill %SKILL_SCRIPT_PATH%

echo.
echo Allegro session completed.
pause

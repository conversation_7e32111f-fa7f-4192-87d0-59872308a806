;; SKILL Program: 3D Symbol Generator for Cadence Allegro
;; Purpose: Load a symbol and generate its 3D model
;; Author: Generated for APL integration project

;; Global variables
(setq *symbol_name* nil)
(setq *output_path* "./3d_models/")
(setq *log_file* "3d_generation.log")

;; Function to log messages
(defun log_message (message)
  (let ((log_port (outfile *log_file* "a")))
    (when log_port
      (fprintf log_port "%s: %s\n" (getCurrentTime) message)
      (close log_port))
    (printf "%s\n" message)))

;; Function to create output directory
(defun create_output_dir ()
  (when (not (isDir *output_path*))
    (createDir *output_path*)
    (log_message (sprintf "Created output directory: %s" *output_path*))))

;; Function to get symbol information
(defun get_symbol_info (symbol_name)
  (let ((symbol_obj nil)
        (symbol_info nil))
    (setq symbol_obj (dbFindAnyInstByName (geGetEditCellView) symbol_name))
    (when symbol_obj
      (setq symbol_info (list
        'name (dbGetq symbol_obj name)
        'cellName (dbGetq symbol_obj cellName)
        'libName (dbGetq symbol_obj libName)
        'bbox (dbGetq symbol_obj bBox)
        'transform (dbGetq symbol_obj transform)))
      (log_message (sprintf "Found symbol: %s" symbol_name))
      symbol_info)))

;; Function to extract 3D properties from symbol
(defun extract_3d_properties (symbol_obj)
  (let ((height 1.6)  ; Default PCB thickness in mm
        (width 0)
        (length 0)
        (bbox nil))
    (when symbol_obj
      (setq bbox (dbGetq symbol_obj bBox))
      (when bbox
        (setq width (abs (difference (caadr bbox) (caar bbox))))
        (setq length (abs (difference (cadadr bbox) (cadar bbox))))
        (log_message (sprintf "Symbol dimensions: W=%.3f L=%.3f H=%.3f" 
                             width length height))))
    (list width length height)))

;; Function to generate 3D model data
(defun generate_3d_model (symbol_name symbol_info dimensions)
  (let ((model_file (strcat *output_path* symbol_name ".3d"))
        (width (car dimensions))
        (length (cadr dimensions))
        (height (caddr dimensions))
        (output_port nil))
    
    (setq output_port (outfile model_file "w"))
    (when output_port
      ;; Write 3D model header
      (fprintf output_port "# 3D Model for Symbol: %s\n" symbol_name)
      (fprintf output_port "# Generated by Allegro SKILL Script\n")
      (fprintf output_port "# Dimensions: %.3f x %.3f x %.3f mm\n\n" 
               width length height)
      
      ;; Write basic 3D geometry (simplified box model)
      (fprintf output_port "# Vertices (8 corners of rectangular box)\n")
      (fprintf output_port "v 0.000 0.000 0.000\n")
      (fprintf output_port "v %.3f 0.000 0.000\n" width)
      (fprintf output_port "v %.3f %.3f 0.000\n" width length)
      (fprintf output_port "v 0.000 %.3f 0.000\n" length)
      (fprintf output_port "v 0.000 0.000 %.3f\n" height)
      (fprintf output_port "v %.3f 0.000 %.3f\n" width height)
      (fprintf output_port "v %.3f %.3f %.3f\n" width length height)
      (fprintf output_port "v 0.000 %.3f %.3f\n" length height)
      
      ;; Write faces (6 faces of the box)
      (fprintf output_port "\n# Faces\n")
      (fprintf output_port "f 1 2 3 4\n")  ; Bottom
      (fprintf output_port "f 5 8 7 6\n")  ; Top
      (fprintf output_port "f 1 5 6 2\n")  ; Front
      (fprintf output_port "f 3 7 8 4\n")  ; Back
      (fprintf output_port "f 1 4 8 5\n")  ; Left
      (fprintf output_port "f 2 6 7 3\n")  ; Right
      
      (close output_port)
      (log_message (sprintf "3D model saved to: %s" model_file))
      model_file)))

;; Function to export to common 3D formats
(defun export_3d_formats (symbol_name model_file)
  (let ((step_file (strcat *output_path* symbol_name ".step"))
        (obj_file (strcat *output_path* symbol_name ".obj")))
    
    ;; Create STEP file (simplified)
    (let ((step_port (outfile step_file "w")))
      (when step_port
        (fprintf step_port "ISO-10303-21;\n")
        (fprintf step_port "HEADER;\n")
        (fprintf step_port "FILE_DESCRIPTION(('3D Model for %s'),'2;1');\n" symbol_name)
        (fprintf step_port "FILE_NAME('%s','%s',('Allegro SKILL'),('Generated'),'','','');\n" 
                 step_file (getCurrentTime))
        (fprintf step_port "ENDSEC;\n")
        (fprintf step_port "DATA;\n")
        (fprintf step_port "ENDSEC;\n")
        (fprintf step_port "END-ISO-10303-21;\n")
        (close step_port)
        (log_message (sprintf "STEP file created: %s" step_file))))
    
    (list step_file obj_file)))

;; Main function to process symbol and generate 3D model
(defun generate_symbol_3d (symbol_name)
  (let ((symbol_info nil)
        (dimensions nil)
        (model_file nil)
        (export_files nil))
    
    (log_message (sprintf "Starting 3D generation for symbol: %s" symbol_name))
    (create_output_dir)
    
    ;; Get symbol information
    (setq symbol_info (get_symbol_info symbol_name))
    (unless symbol_info
      (log_message (sprintf "ERROR: Symbol '%s' not found" symbol_name))
      (return nil))
    
    ;; Extract dimensions
    (setq dimensions (extract_3d_properties 
                      (dbFindAnyInstByName (geGetEditCellView) symbol_name)))
    
    ;; Generate 3D model
    (setq model_file (generate_3d_model symbol_name symbol_info dimensions))
    
    ;; Export to other formats
    (setq export_files (export_3d_formats symbol_name model_file))
    
    (log_message "3D model generation completed successfully")
    (printf "3D model files generated in: %s\n" *output_path*)
    
    t))

;; Interactive function to get symbol name from user
(defun prompt_for_symbol ()
  (let ((symbol_name nil))
    (setq symbol_name (askString "Enter symbol name to generate 3D model:"))
    (when (and symbol_name (not (equal symbol_name "")))
      (setq *symbol_name* symbol_name)
      (generate_symbol_3d symbol_name))))

;; Auto-run if symbol name is provided
(when (boundp '*symbol_name*)
  (if *symbol_name*
    (generate_symbol_3d *symbol_name*)
    (prompt_for_symbol)))

;; If no symbol specified, prompt user
(unless (boundp '*symbol_name*)
  (prompt_for_symbol))

(printf "3D Symbol Generator loaded. Use (generate_symbol_3d \"symbol_name\") to generate models.\n")
